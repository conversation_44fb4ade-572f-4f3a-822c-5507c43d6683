import 'package:cp_associates/features/project/domain/entity/project_model.dart';

abstract class ProjectRepo {
  // CRUD FUNCTIONS
  Future<void> createProject(ProjectModel project);
  Future<void> updateProject(ProjectModel project);
  Future<void> deleteProject(String docId);

  // FETCH FUNCTIONS

  // Fetch all projects for admin
  Stream<List<ProjectModel>> getAllProjects();

  // Fetch all projects of current user
  Stream<List<ProjectModel>> getProjectsOfCurrentUser();

  // Fetch all non finished projects
  Future<List<ProjectModel>> getAllNonFinishedProjects();

  // Fetch all completed projects between two dates
  Future<List<ProjectModel>> getAllFinishedProject(
    DateTime start,
    DateTime end,
  );

  Future<ProjectModel?> getProjectById(String id);
  Future<String> getTotalProjectCount();
}
