import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_container.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProjectDetailTile extends StatefulWidget {
  bool isMobile;
  ProjectDetailTile({super.key, required this.isMobile});

  @override
  State<ProjectDetailTile> createState() => _ProjectDetailTileState();
}

class _ProjectDetailTileState extends State<ProjectDetailTile> {
  @override
  void initState() {
    super.initState();

    // Only fetch if not already loaded
    final cubit = context.read<ProjectCubit>();

    cubit.filterCompletedProjects(filterType: FinishedProjectFilter.Monthly);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProjectCubit, ProjectState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        // print("--");
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.filteredProjects.isEmpty) {
          return Center(child: Text("No Project Found"));
        } else if (state.filteredProjects.isNotEmpty) {
          return SingleChildScrollView(
            child: StaggeredGrid.extent(
              maxCrossAxisExtent: 530,
              mainAxisSpacing: 15,
              crossAxisSpacing: 30,
              children: [
                ...List.generate(state.filteredProjects.length, (index) {
                  final project = state.filteredProjects[index];
                  return GestureDetector(
                    onDoubleTap: () {
                      kIsWeb
                          ? showDialog(
                            context: context,
                            builder: (context) {
                              return Dialog(
                                child: Container(
                                  width: 800,
                                  child: BlocProvider(
                                    create:
                                        (context) => ProjectFormCubit(
                                          context.read<ProjectCubit>().repo,
                                        ),
                                    child: ProjectForm(
                                      editProject: project,
                                      isMobile: false,
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                          : showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            useSafeArea: true,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.of(context).viewInsets,

                                child: BlocProvider(
                                  create:
                                      (context) => ProjectFormCubit(
                                        context.read<ProjectCubit>().repo,
                                      ),
                                  child: ProjectForm(
                                    editProject: project,
                                    isMobile: true,
                                  ),
                                ),
                              );
                            },
                          );
                    },
                    onLongPress: () {
                      showConfirmDeletDialog(context, () {
                        // projectCubit.delete(project.docId);
                        context.read<ProjectCubit>().deleteProject(
                          project.docId,
                        );
                      });
                    },
                    onTap: () {
                      kIsWeb
                          ? context.go(
                            "${Routes.activity}/${project.docId}",
                            extra: false,
                          )
                          : context.push(
                            "${Routes.activity}/${project.docId}",
                            extra: false,
                          );
                    },
                    child: ProjectDetailContainer(
                      isMobile: widget.isMobile,
                      project: project,
                    ),
                  );
                }),
              ],
            ),
          );
        } else {
          return Center(child: Text("No Project Found"));
        }
      },
    );
  }
}
