import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseProjectRepo implements ProjectRepo {
  final projectsRef = FBFireStore.projects;

  // CRUD FUNCTIONS
  @override
  Future<void> createProject(ProjectModel project) async {
    final docRef = projectsRef.doc();
    final newProject = project.copyWith(docId: docRef.id);
    await docRef.set(newProject.toJson());
  }

  @override
  Future<void> updateProject(ProjectModel project) async {
    await projectsRef.doc(project.docId).update(project.toJson());
  }

  @override
  Future<void> deleteProject(String docId) async {
    await projectsRef.doc(docId).delete();
  }

  // Fetch Methods

  // Fetch all projects for admin
  @override
  Stream<List<ProjectModel>> getAllProjects() {
    return projectsRef
        .where('projectStatus', isNotEqualTo: 'Finished')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => ProjectModel.fromSnapshot(doc))
              .toList();
        });
  }

  // Fetch all projects of current user
  @override
  Stream<List<ProjectModel>> getProjectsOfCurrentUser() {
    return projectsRef
        .where(
          Filter.or(
            Filter('userId', arrayContains: FBAuth.auth.currentUser?.uid ?? ''),
            Filter('createdBy', isEqualTo: FBAuth.auth.currentUser?.uid ?? ''),
          ),
        )
        .where('projectStatus', whereIn: ['Active', 'on-Hold'])
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => ProjectModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<List<ProjectModel>> getAllNonFinishedProjects() async {
    return projectsRef
        .where('projectStatus', isNotEqualTo: 'Finished')
        .get()
        .then(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProjectModel.fromSnapshot(doc))
                  .toList(),
        );
  }

  // Fetch all completed projects between two dates
  @override
  Future<List<ProjectModel>> getAllFinishedProject(
    DateTime start,
    DateTime end,
  ) async {
    return projectsRef
        .where('projectStatus', isEqualTo: 'Finished')
        .where('projectStatusUpdateAt', isGreaterThanOrEqualTo: start)
        .where('projectStatusUpdateAt', isLessThan: end)
        .get()
        .then(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProjectModel.fromSnapshot(doc))
                  .toList(),
        );
  }

  @override
  Future<ProjectModel?> getProjectById(String id) async {
    final doc = await projectsRef.doc(id).get();

    if (!doc.exists) {
      return null;
    }

    final project = ProjectModel.fromSnapshot(doc);

    return project;
  }

  Future<String> getTotalProjectCount() async {
    final countSnapshot = await projectsRef.count().get();
    return countSnapshot.count.toString();
  }
}
