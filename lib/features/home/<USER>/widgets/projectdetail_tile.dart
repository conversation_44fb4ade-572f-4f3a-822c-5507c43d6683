import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_container.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_tile.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProjectDetailTile extends StatefulWidget {
  bool isMobile;
  ProjectDetailTile({super.key, required this.isMobile});

  @override
  State<ProjectDetailTile> createState() => _ProjectDetailTileState();
}

class _ProjectDetailTileState extends State<ProjectDetailTile> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      child: BlocConsumer<ProjectCubit, ProjectState>(
        listener: (context, state) {
          if (state.message.isNotEmpty) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        builder: (context, state) {
          // print("--");
          if (state.isLoading) {
            return Center(child: CircularProgressIndicator());
          } else if (state.projects.isEmpty) {
            return Center(
              child: Column(
                children: [
                  SizedBox(height: 20),
                  Text("No Project Found"),
                  SizedBox(height: 20),
                ],
              ),
            );
          } else if (state.projects.isNotEmpty) {
            return SingleChildScrollView(
              child: StaggeredGrid.extent(
                maxCrossAxisExtent: 530,
                mainAxisSpacing: 15,
                crossAxisSpacing: 30,
                children: [
                  ...List.generate(state.projects.length, (index) {
                    final project = state.projects[index];
                    return TransparentInkWell(
                      onDoubleTap: () {
                        kIsWeb
                            ? showDialog(
                              context: context,
                              builder: (context) {
                                return Dialog(
                                  child: Container(
                                    width: 800,
                                    child: BlocProvider(
                                      create:
                                          (context) => ProjectFormCubit(
                                            context.read<ProjectCubit>().repo,
                                          ),
                                      child: ProjectForm(
                                        editProject: project,
                                        isMobile: false,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )
                            : showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              useSafeArea: true,
                              builder: (context) {
                                return Padding(
                                  padding: MediaQuery.of(context).viewInsets,

                                  child: BlocProvider(
                                    create:
                                        (context) => ProjectFormCubit(
                                          context.read<ProjectCubit>().repo,
                                        ),
                                    child: ProjectForm(
                                      editProject: project,
                                      isMobile: true,
                                    ),
                                  ),
                                );
                              },
                            );
                      },
                      onLongPress: () {
                        showConfirmDeletDialog(context, () {
                          // projectCubit.delete(project.docId);
                          context.read<ProjectCubit>().deleteProject(
                            project.docId,
                          );
                        });
                      },
                      onTap: () {
                        context.read<ProjectCubit>().createLastSeen(
                          project.docId,
                        );
                        kIsWeb
                            ? context.go(
                              "${Routes.task}/${project.docId}/",
                              extra: true,
                            )
                            : context.push(
                              "${Routes.task}/${project.docId}",
                              extra: true,
                            );
                      },
                      child: ProjectDetailContainer(
                        isMobile: widget.isMobile,
                        project: project,
                      ),
                    );
                  }),
                ],
              ),
            );
          } else {
            return Center();
          }
        },
      ),
    );
  }
}

class DotIndicator extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 12,
      width: 12,
      decoration: BoxDecoration(
        color: Colors.green,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
    );
  }
}
