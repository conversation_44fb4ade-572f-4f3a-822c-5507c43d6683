import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/widget/attendance_container.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:cp_associates/features/home/<USER>/widgets/row_icontext_tile.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:cp_associates/features/home/<USER>/widgets/projectdetail_tile.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../auth/presentation/cubits/auth_state.dart';

class HomePage extends StatefulWidget {
  HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    // context.read<AuthCubit>().state.currentUser?.role == "admin"
    //     ? {context.read<ProjectCubit>().fetchProjects()}
    //     : {context.read<ProjectCubit>().fetchProjectsOfCurrentUser()};
    // context.read<UserCubit>().fetchAllUsers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // print("--");
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final user = state.currentUser;
        final authCubit = context.read<AuthCubit>();
        return ResponsiveWid(
          mobile: Scaffold(
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),

            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Center(
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.sizeOf(context).width,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(height: 16),
                        AttendanceContainer(isMobile: true),
                        SizedBox(height: 30),

                        BlocBuilder<AdminTaskCubit, AdminTaskState>(
                          builder: (context, state) {
                            return RowIconTextTile(
                              title:
                                  authCubit.state.currentUser?.role == "admin"
                                      ? "My Tasks (${state.adminTasks.length})"
                                      : "Admin To Do (${state.adminTasks.length})",
                              SuffixWidget: Image.asset(
                                "assets/images/logo_light.png",
                                width: 30,
                                height: 30,
                              ),
                              onTap: () {
                                context.push(Routes.adminTask);
                              },
                              isMobile: true,
                              isLoading: false,
                            );
                          },
                        ),

                        SizedBox(height: 10),
                        projectHeading(context),
                        ProjectDetailTile(isMobile: true),
                        SizedBox(height: 10),
                        InkWell(
                          onTap: () {
                            context.push(Routes.projects);
                          },
                          child: RowIconTextTile(
                            title: "All Projects",
                            SuffixWidget: Icon(
                              color: Colors.white,
                              CupertinoIcons.doc_plaintext,
                              size: 30,
                            ),
                            onTap: () {
                              context.push(Routes.projects);
                            },
                            isMobile: true,
                            isLoading: false,
                          ),
                        ),

                        SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          desktop: Scaffold(
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),
            body: GestureDetector(
              onTap: () {
                final settingCubit = context.read<SettingCubit>();
                if (settingCubit.controller.isShowing) {
                  settingCubit.controller.toggle();
                } else {
                  null;
                }
              },
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 100,
                    vertical: 50,
                  ),

                  child: Column(
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 240,
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Container(
                                child: AttendanceContainer(isMobile: false),
                              ),
                            ),
                            SizedBox(width: 20),
                            state.currentUser?.role == "admin"
                                ? Expanded(
                                  child: Column(
                                    children: [
                                      Expanded(
                                        child:
                                            BlocBuilder<UserCubit, UserState>(
                                              builder: (context, state) {
                                                return RowIconTextTile(
                                                  title: "Total Users",
                                                  count:
                                                      state.users.length
                                                          .toString(),
                                                  SuffixWidget: Icon(
                                                    CupertinoIcons.person_2,
                                                    size: 40,
                                                  ),
                                                  onTap: () {
                                                    context.go(Routes.users);
                                                  },
                                                  isMobile: false,
                                                  isLoading: state.isLoading,
                                                );
                                              },
                                            ),
                                      ),
                                      SizedBox(height: 20),
                                      Expanded(
                                        child: BlocBuilder<
                                          AttendanceAdminCubit,
                                          AttendanceAdminState
                                        >(
                                          builder: (context, state) {
                                            final totalPresentUsers =
                                                state.punches.where((punches) {
                                                  return punches.punchIn;
                                                }).length;
                                            return RowIconTextTile(
                                              title: "Present Users",
                                              count:
                                                  totalPresentUsers.toString(),
                                              SuffixWidget: Icon(
                                                CupertinoIcons.person_2,
                                                size: 40,
                                              ),
                                              onTap: () {
                                                context.go(
                                                  Routes.userAttendance,
                                                );
                                              },
                                              isMobile: false,
                                              isLoading: state.isLoading,
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                                : Expanded(
                                  child: BlocBuilder<UserCubit, UserState>(
                                    builder: (context, state) {
                                      return Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 10,
                                          vertical: 20,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppColors.borderGrey,
                                          ),
                                          color: AppColors.containerGreyColor,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(
                                                  CupertinoIcons.person_2,
                                                  size: 40,
                                                ),
                                                SizedBox(width: 20),
                                                Text(
                                                  "Total Users",
                                                  style: TextStyle(
                                                    fontSize: 20,
                                                    fontWeight: FontWeight.w900,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Expanded(
                                              child: Center(
                                                child: Text(
                                                  state.users.length.toString(),
                                                  style: TextStyle(
                                                    fontSize: 30,
                                                    fontWeight: FontWeight.w900,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                            SizedBox(width: 20),
                            Expanded(
                              child: Column(
                                children: [
                                  Expanded(
                                    child: BlocBuilder<
                                      AdminTaskCubit,
                                      AdminTaskState
                                    >(
                                      builder: (context, state) {
                                        return RowIconTextTile(
                                          title: "Admin Task",
                                          count:
                                              state.adminTasks.length
                                                  .toString(),

                                          SuffixWidget: Icon(
                                            color: Colors.black,
                                            Icons.checklist,
                                            size: 30,
                                          ),
                                          onTap: () {
                                            context.go(Routes.adminTask);
                                          },
                                          isMobile: false,
                                          isLoading: state.isLoading,
                                        );
                                      },
                                    ),
                                  ),
                                  SizedBox(height: 20),
                                  Expanded(
                                    child:
                                        BlocBuilder<ProjectCubit, ProjectState>(
                                          builder: (context, state) {
                                            return RowIconTextTile(
                                              title: "All Projects",
                                              count: state.totalProjectCount,
                                              SuffixWidget: Icon(
                                                color: Colors.black,
                                                CupertinoIcons.doc_plaintext,
                                                size: 30,
                                              ),
                                              onTap: () {
                                                context.go(Routes.projects);
                                              },
                                              isMobile: false,
                                              isLoading: state.isLoading,
                                            );
                                          },
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 20),
                      projectHeading(context),
                      ProjectDetailTile(isMobile: false),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Row projectHeading(BuildContext context) {
    return Row(
      children: [
        Text("Active Projects", style: AppTextStyles.heading2),
        Spacer(),
        IconButton(
          onPressed: () {
            kIsWeb
                ? showDialog(
                  context: context,
                  builder: (context) {
                    return Dialog(
                      child: Container(
                        constraints: BoxConstraints(maxWidth: 800),
                        child: BlocProvider(
                          create:
                              (context) => ProjectFormCubit(
                                context.read<ProjectCubit>().repo,
                              ),
                          child: ProjectForm(
                            editProject: null,
                            isMobile: false,
                          ),
                        ),
                      ),
                    );
                  },
                )
                : showModalBottomSheet(
                  useSafeArea: true,
                  context: context,
                  isScrollControlled: true,
                  builder: (context) {
                    return Padding(
                      padding: MediaQuery.of(context).viewInsets,
                      child: BlocProvider(
                        create:
                            (context) => ProjectFormCubit(
                              context.read<ProjectCubit>().repo,
                            ),
                        child: SingleChildScrollView(
                          child: ProjectForm(editProject: null, isMobile: true),
                        ),
                      ),
                    );
                  },
                );
            // : showProjectBottomSheet(context, null);
          },
          icon: Icon(CupertinoIcons.add),
        ),
      ],
    );
  }
}
