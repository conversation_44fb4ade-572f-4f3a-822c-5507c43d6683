part of 'setting_cubit.dart';

class SettingState {
  final SettingsModel? settings;
  final bool versionSupported;

  SettingState({required this.settings, required this.versionSupported});

  factory SettingState.initial() {
    return SettingState(settings: null, versionSupported: false);
  }

  SettingState copyWith({SettingsModel? settings, bool? versionSupported}) {
    return SettingState(
      settings: settings ?? this.settings,
      versionSupported: versionSupported ?? this.versionSupported,
    );
  }
}
