import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/features/home/<USER>/antity/setting_model.dart';
import 'package:cp_associates/features/home/<USER>/repo/setting_repo.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

part 'setting_state.dart';

class SettingCubit extends Cubit<SettingState> {
  final SettingRepo settingRepo;
  SettingCubit(this.settingRepo) : super(SettingState.initial());

  final GlobalKey avatarKey = GlobalKey();
  final OverlayPortalController controller = OverlayPortalController();

  //get Setting stream
  StreamSubscription<SettingsModel?>? settingsStream;
  StreamSubscription<InternetStatus>? internetStream;
  void getSettings() {
    settingsStream?.cancel();
    settingsStream = settingRepo.getSettings().listen((settings) {
      if (isClosed) return;
      emit(state.copyWith(settings: settings));
      checkVersionSupported();
    });
  }

  void startInternetMonitoring() {
    internetStream?.cancel();
    internetStream = InternetConnection().onStatusChange.listen((status) {
      if (isClosed) return;
      final hasConnection = status == InternetStatus.connected;
      print("Internet connection status: $hasConnection");
      emit(state.copyWith(hasInternetConnection: hasConnection));
    });
  }

  Future<void> checkInitialInternetConnection() async {
    final hasConnection = await InternetConnection().hasInternetAccess;
    if (isClosed) return;
    emit(state.copyWith(hasInternetConnection: hasConnection));
  }

  void checkVersionSupported() async {
    if (state.settings == null) return;
    final packageInfo = await PackageInfo.fromPlatform();
    String currentBuildVersion = packageInfo.buildNumber;
    print("currentBuildVersion: $currentBuildVersion");
    String firebaseBuildVersion = state.settings!.andbuildNumber;
    bool versionSupported =
        int.parse(currentBuildVersion) >= int.parse(firebaseBuildVersion);
    print("versionSupported: $versionSupported");

    if (isClosed) return;

    emit(state.copyWith(versionSupported: versionSupported));
  }

  @override
  Future<void> close() {
    settingsStream?.cancel();
    internetStream?.cancel();
    return super.close();
  }
}
