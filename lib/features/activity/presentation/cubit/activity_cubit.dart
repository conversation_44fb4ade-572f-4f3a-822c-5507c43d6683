import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  final ActivityRepo activityRepo;
  ActivityCubit(this.activityRepo) : super(ActivityState.initial());
  StreamSubscription<List<ActivityModel>>? activityStream;
  ScrollController scrollController = ScrollController();

  void viewActivity(ActivityModel activity, BuildContext context) async {
    await viewFile(
      context: context,
      selectedFile: null,
      dbImg: activity.attachment,
      dbImgExt: activity.attachmentType,
    );
  }

  void fetchActivities(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));
    print("fetchActivities----1");

    activityStream?.cancel();

    activityStream = activityRepo
        .fetchProjectActivity(projectId)
        .listen(
          (activities) {
            print("fetchActivities----2");
            activities.sort((a, b) => a.sendAt.compareTo(b.sendAt));
            emit(
              state.copyWith(
                activities: activities,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch activities: ${error.toString()}",
              ),
            );
          },
        );
  }

  void closeKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  // LAZY LOADING
  // Real-time stream for initial messages
  void subscribeInitialActivities(String projectId) {
    emit(state.copyWith(isLoading: true, message: "", hasReachedEnd: false));
    activityStream?.cancel();
    activityStream = activityRepo
        .fetchInitialActivities(projectId: projectId, limit: 10)
        .listen(
          (activities) {
            print("fetchInitialActivities----${activities.length}");
            activities.sort((a, b) => a.sendAt.compareTo(b.sendAt));

            emit(
              state.copyWith(
                activities: activities,
                isLoading: false,
                message: "",
                hasReachedEnd:
                    activities.length <
                    10, // If less than limit, we've reached the end
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch activities: ${error.toString()}",
              ),
            );
          },
        );
  }

  // Fetch older messages (pagination)
  void loadMoreActivities(String projectId) async {
    if (state.isLoadingMore || state.hasReachedEnd || state.activities.isEmpty)
      return;

    emit(state.copyWith(isLoadingMore: true, message: ""));
    try {
      final moreActivities = await activityRepo.fetchMoreActivities(
        projectId: projectId,
        limit: 10,
        startAfter:
            state
                .activities
                .first
                .sendAt, // Use first since we're loading older messages
      );

      if (moreActivities.isEmpty) {
        emit(state.copyWith(hasReachedEnd: true, isLoadingMore: false));
        return;
      }

      moreActivities.sort((a, b) => a.sendAt.compareTo(b.sendAt));

      // Add older messages at the beginning of the list
      emit(
        state.copyWith(
          activities: [...moreActivities, ...state.activities],
          isLoadingMore: false,
          message: "",
          hasReachedEnd: moreActivities.length < 10,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoadingMore: false,
          message: "Failed to fetch more activities: ${e.toString()}",
        ),
      );
    }
  }

  @override
  Future<void> close() {
    activityStream?.cancel();
    return super.close();
  }
}
