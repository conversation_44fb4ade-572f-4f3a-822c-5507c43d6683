import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:cp_associates/features/activity/presentation/widget/activity_detail_tile.dart';
import 'package:cp_associates/features/activity/presentation/widget/activity_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityPage extends StatefulWidget {
  ActivityPage({super.key, required this.projectId});
  final String? projectId;

  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> {
  @override
  void initState() {
    super.initState();
    context.read<ActivityCubit>().fetchActivities(widget.projectId ?? "");
  }

  @override
  Widget build(BuildContext context) {
    print("ActivityPage build");
    final activityCubit = context.read<ActivityCubit>();
    return ResponsiveCustomBuilder(
      mobileBuilder: (width) {
        return Padding(
          padding: const EdgeInsets.all(15),
          child: GestureDetector(
            onTap: () {
              activityCubit.closeKeyboard(context);
            },
            child: Column(
              children: [
                Expanded(
                  child: ActivityDetailTile(projectId: widget.projectId ?? ""),
                ),
                BlocProvider(
                  create:
                      (context) =>
                          ActivityFormCubit(activityCubit.activityRepo),
                  child: ActivityForm(projectId: widget.projectId ?? ""),
                ),
              ],
            ),
          ),
        );
      },
      desktopBuilder: (width) {
        return Column(
          children: [
            Expanded(
              child: ActivityDetailTile(projectId: widget.projectId ?? ""),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: Align(
                alignment: Alignment.bottomRight,
                child: BlocProvider(
                  create:
                      (context) =>
                          ActivityFormCubit(activityCubit.activityRepo),
                  child: ActivityForm(projectId: widget.projectId ?? ""),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
