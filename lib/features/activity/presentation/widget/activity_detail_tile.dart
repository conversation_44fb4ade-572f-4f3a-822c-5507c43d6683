import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/activity/presentation/widget/message_bubble_cliper.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityDetailTile extends StatefulWidget {
  String projectId;
  ActivityDetailTile({super.key, required this.projectId});

  @override
  State<ActivityDetailTile> createState() => _ActivityDetailTileState();
}

class _ActivityDetailTileState extends State<ActivityDetailTile> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Check if user scrolled to the top (for loading older messages)
    if (_scrollController.position.pixels <= 100 &&
        !_scrollController.position.outOfRange) {
      final activityCubit = context.read<ActivityCubit>();
      if (!activityCubit.state.isLoading &&
          !activityCubit.state.hasReachedEnd) {
        activityCubit.loadMoreActivities(widget.projectId);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    context.read<ActivityCubit>();
    return BlocConsumer<ActivityCubit, ActivityState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        // print("Activit Cubit build");
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.activities.isEmpty) {
          return Center(child: Text("No Activity Avaliable"));
        } else {
          DateTime? previousDate;
          return SingleChildScrollView(
            controller: _scrollController,
            reverse: true,
            physics: ClampingScrollPhysics(),
            dragStartBehavior: DragStartBehavior.down,
            child: Column(
              spacing: 10,
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
                // Loading indicator at the top when loading more messages
                if (state.isLoading && state.activities.isNotEmpty)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  ),
                // End of messages indicator
                if (state.hasReachedEnd && state.activities.isNotEmpty)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        "No more messages",
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ),
                  ),
                ...List.generate(state.activities.length, (index) {
                  final activity = state.activities[index];
                  final isSender =
                      activity.senderId == FBAuth.auth.currentUser?.uid;

                  final currentDate = DateTime(
                    activity.sendAt.year,
                    activity.sendAt.month,
                    activity.sendAt.day,
                  );

                  bool showDateHeader = false;
                  if (previousDate == null || currentDate != previousDate) {
                    showDateHeader = true;
                    previousDate = currentDate;
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (showDateHeader)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: 5,
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.containerGreyColor,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: AppColors.borderGrey),
                              ),
                              child: Text(getDateLabel(currentDate)),
                            ),
                          ),
                        ),
                      Align(
                        alignment:
                            isSender
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: 10,
                                left: isSender ? 0 : 35,
                                right: isSender ? 35 : 0,
                                bottom:
                                    state.activities.length - 1 == index
                                        ? 15
                                        : 0,
                              ),
                              child: ClipPath(
                                clipper: MessageBubbleClipper(
                                  isSender: isSender,
                                ),
                                child: Container(
                                  padding: EdgeInsets.only(
                                    bottom: 1,
                                    left: isSender ? 10 : 20,
                                    right: isSender ? 20 : 10,
                                    top: 0,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        isSender
                                            ? AppColors.msg
                                            : Colors.grey.shade200,
                                  ),
                                  child: messageContent(activity),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 0,
                              left: isSender ? null : 0,
                              right: isSender ? 0 : null,
                              child: CircleAvatar(
                                radius: 15,
                                child: Text(
                                  userCubit
                                          .getUserById(activity.senderId)
                                          ?.name[0]
                                          .toUpperCase() ??
                                      "",
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }),
              ],
            ),
          );
        }
      },
    );
  }

  Widget messageContent(ActivityModel activity) {
    final isSender = FBAuth.auth.currentUser?.uid == activity.senderId;
    final userName =
        context.read<UserCubit>().getUserById(activity.senderId)?.name ?? "";

    final hasMessage = (activity.message ?? "").isNotEmpty;
    final hasFile = activity.attachment != null;

    return Container(
      constraints: BoxConstraints(maxWidth: 280),
      padding: EdgeInsets.only(),
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(
              // left: 10,
              right: hasFile ? 10 : 60, // leave space for time
              top: 4,
              bottom: 3, // leave space for time
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ✅ Show sender name only if not me
                if (!isSender)
                  Text(
                    userName,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                // SizedBox(height: 4),

                // ✅ CASE: File + Message
                if (hasFile && hasMessage) ...[
                  buildFilePreview(
                    context: context,
                    selectedFile: null,
                    dbFile: activity.attachment,
                    dbFileExt: activity.attachmentType,
                    dbFileName: activity.attachmentName,
                    isEdit: false,
                    onDelete: () {},
                    onView: () {
                      context.read<ActivityCubit>().viewActivity(
                        activity,
                        context,
                      );
                    },
                    isMessage: true,
                  ),
                  // SizedBox(height: ),
                  Text(activity.message!),
                ]
                // ✅ CASE: Only File
                else if (hasFile)
                  buildFilePreview(
                    context: context,
                    selectedFile: null,
                    dbFile: activity.attachment,
                    dbFileExt: activity.attachmentType,
                    dbFileName: activity.attachmentName,
                    isEdit: false,
                    onDelete: () {},
                    onView: () {
                      context.read<ActivityCubit>().viewActivity(
                        activity,
                        context,
                      );
                    },
                    isMessage: true,
                  )
                // ✅ CASE: Only Message
                else if (hasMessage)
                  Text(activity.message ?? ""),
              ],
            ),
          ),

          // ✅ Time in bottom-right inside bubble
          Positioned(
            bottom: 2,
            right: 6,
            child: Text(
              activity.sendAt.goodTime(),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
