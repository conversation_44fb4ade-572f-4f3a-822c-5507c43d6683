import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

abstract class ActivityRepo {
  Future<void> createActivity(ActivityModel activity);
  Future<void> updateActivity(ActivityModel activity);
  Future<void> deleteActivity(String activityId);
  Stream<List<ActivityModel>> fetchProjectActivity(String projectId);

  // LAZY LOADING

  // Real-time stream for initial messages
  Stream<List<ActivityModel>> fetchInitialActivities({
    required String projectId,
    required int limit,
  });

  // Fetch older messages (pagination)
  Future<List<ActivityModel>> fetchMoreActivities({
    required String projectId,
    required int limit,
    required DateTime startAfter,
    required DocumentSnapshot lastDocSnapshot,
  });
}
