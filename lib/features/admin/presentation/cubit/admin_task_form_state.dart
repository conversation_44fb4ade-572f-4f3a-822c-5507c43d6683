part of 'admin_task_form_cubit.dart';

@immutable
class AdminTaskFormState {
  final String message;
  final bool isLoading;
  final String? selectedProject;
  final DateTime? dueDate;
  final TimeOfDay? dueTime;

  AdminTaskFormState({
    required this.message,
    required this.isLoading,
    required this.selectedProject,
    required this.dueDate,
    required this.dueTime,
  });

  factory AdminTaskFormState.initial() {
    return AdminTaskFormState(
      message: '',
      isLoading: false,
      selectedProject: null,
      dueDate: null,
      dueTime: null,
    );
  }

  AdminTaskFormState copyWith({
    String? message,
    bool? isLoading,
    String? selectedProject,
    DateTime? dueDate,
    TimeOfDay? dueTime,
  }) {
    return AdminTaskFormState(
      selectedProject: selectedProject ?? this.selectedProject,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      dueDate: dueDate ?? this.dueDate,
      dueTime: dueTime ?? this.dueTime,
    );
  }
}
