import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/domain/repo/admin_repo.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';

part 'admin_task_state.dart';

class AdminTaskCubit extends Cubit<AdminTaskState> {
  AdminTaskCubit(this.adminTaskRepo) : super(AdminTaskState.initial());

  AdminTaskRepo adminTaskRepo;
  StreamSubscription<List<AdminTaskModel>>? adminTaskStream;

  // FETCH METHODS

  // --methods if admin
  // ---fetch all admin task
  void fetchAllAdminTask() {
    emit(state.copyWith(isLoading: true, message: ""));

    adminTaskStream?.cancel();

    adminTaskStream = adminTaskRepo.getAllAdminTask().listen(
      (adminTask) {
        print("AllAdminTaskStream-${adminTask.length}");
        adminTask.sort((a, b) {
          DateTime aDate = a.dueDate ?? a.createdAt;
          DateTime bDate = b.dueDate ?? b.createdAt;
          return aDate.compareTo(bDate);
        });

        emit(
          state.copyWith(adminTasks: adminTask, isLoading: false, message: ""),
        );
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch admin task: ${error.toString()}",
          ),
        );
      },
    );
  }

  // --fetch completed admin task
  void fetchCompletedAdminTask(DateTime month) {
    emit(state.copyWith(isLoading: true, message: ""));

    adminTaskRepo
        .getCompletedTask(month)
        .then((adminTask) {
          emit(
            state.copyWith(
              filteredAdminTasks: adminTask,
              isLoading: false,
              message: "",
            ),
          );
        })
        .catchError((error) {
          print(error.toString());
          emit(
            state.copyWith(
              isLoading: false,
              message: "Failed to fetch admin task: ${error.toString()}",
            ),
          );
        });
  }

  // --methods if user
  // ---fetch admin task created by current user
  void fetchAdminTaskCreatedByCurrentUser(String userId) {
    emit(state.copyWith(isLoading: true, message: ""));

    adminTaskStream?.cancel();

    adminTaskStream = adminTaskRepo
        .getAdminTaskCreatedByCurrentUser(userId)
        .listen(
          (adminTask) {
            print("AdminTaskCreatedByUserStream--${adminTask.length}");
            emit(
              state.copyWith(
                adminTasks: adminTask,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch admin task: ${error.toString()}",
              ),
            );
          },
        );
  }

  // ---fetch completed admin task created by current user
  void fetchCompletedAdminTaskByCurrentUser(DateTime month, String userId) {
    emit(state.copyWith(isLoading: true, message: ""));
    adminTaskRepo
        .getCompletedAdminTaskByCurrentUser(month, userId)
        .then((adminTask) {
          emit(
            state.copyWith(
              filteredAdminTasks: adminTask,
              isLoading: false,
              message: "",
            ),
          );
        })
        .catchError((error) {
          print(error.toString());
          emit(
            state.copyWith(
              isLoading: false,
              message: "Failed to fetch admin task: ${error.toString()}",
            ),
          );
        });
  }

  void fetchTaskById(String adminTaskId) async {
    emit(state.copyWith(isLoading: true));
    final adminTask = state.adminTasks.firstWhere(
      (task) => task.docId == adminTaskId,
    );
    emit(state.copyWith(isLoading: false, adminTaskDetail: adminTask));
  }

  // CRUD METHODS
  void updateAdminTask(AdminTaskModel adminTask) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await adminTaskRepo.updateAdminTask(adminTask);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task updated successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to update admin task: ${e.toString()}",
        ),
      );
    }
  }

  void deleteAdminTask(String adminTaskId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await adminTaskRepo.deleteAdminTask(adminTaskId);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task deleted successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete admin task: ${e.toString()}",
        ),
      );
    }
  }

  //UI METHODS
  void filterTask(String type, BuildContext context) {
    emit(state.copyWith(isLoading: true, selectedType: type, message: ""));

    if (type == AdminTaskTypes.onGoing) {
      context.read<AuthCubit>().currentUser?.role == "admin"
          ? fetchAllAdminTask()
          : fetchAdminTaskCreatedByCurrentUser(
            FBAuth.auth.currentUser?.uid ?? "",
          );
      // final tasks =
      //     state.adminTasks.where((task) => task.isCompleted == false).toList();
      emit(
        state.copyWith(
          isLoading: false,
          message: "",
          selectedType: AdminTaskTypes.onGoing,
        ),
      );
    } else if (type == AdminTaskTypes.completed) {
      final now = DateTime.now();
      emit(state.copyWith(selectedMonth: now.month));

      context.read<AuthCubit>().currentUser?.role == "admin"
          ? fetchCompletedAdminTask(DateTime(now.year, now.month))
          : fetchCompletedAdminTaskByCurrentUser(
            DateTime(now.year, now.month),
            FBAuth.auth.currentUser?.uid ?? "",
          );

      emit(
        state.copyWith(
          isLoading: false,
          message: "",
          selectedType: AdminTaskTypes.completed,
        ),
      );
    }
  }

  void selectMonth(int value) {
    emit(state.copyWith(selectedMonth: value));
  }

  void getMontlhyTask(DateTime month) {
    emit(state.copyWith(isLoading: true, message: ""));
    if (state.adminTasks.isEmpty) {
      emit(state.copyWith(isLoading: false, message: "No task found"));
      return;
    }
    final tasks =
        state.adminTasks
            .where(
              (task) =>
                  task.createdAt.year == month.year &&
                  task.createdAt.month == month.month,
            )
            .toList();
    emit(
      state.copyWith(filteredAdminTasks: tasks, isLoading: false, message: ""),
    );
  }

  @override
  Future<void> close() {
    adminTaskStream?.cancel();
    return super.close();
  }
}
