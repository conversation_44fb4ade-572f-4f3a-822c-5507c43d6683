import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/domain/repo/admin_repo.dart';
import 'package:flutter/material.dart';

part 'admin_task_form_state.dart';

class AdminTaskFormCubit extends Cubit<AdminTaskFormState> {
  AdminTaskRepo adminTaskRepo;
  AdminTaskFormCubit(this.adminTaskRepo) : super(AdminTaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  // Initialize form
  void initializeForm(AdminTaskModel? editAdminTask) {
    if (editAdminTask != null) {
      titleController.text = editAdminTask.title;
      descController.text = editAdminTask.desc;

      emit(
        state.copyWith(
          selectedProject: editAdminTask.projectId,
          dueDate: editAdminTask.dueDate,
        ),
      );
    } else {
      emit(AdminTaskFormState.initial());
    }
  }

  // UI RELATED FUNCTION
  void selectProject(String projectId) {
    emit(state.copyWith(selectedProject: projectId));
  }

  void selectDueDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    emit(state.copyWith(dueDate: res));
  }

  void selectDueTime(BuildContext context) async {
    final res = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    emit(state.copyWith(dueTime: res));

    // emit(state.copyWith(dueDate: res));
  }

  // Submit
  void submit(AdminTaskModel? editAdminTask, BuildContext context) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        final adminTask = AdminTaskModel(
          docId: editAdminTask?.docId ?? "",
          projectId: state.selectedProject,
          createdAt: editAdminTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          title: titleController.text,
          desc: descController.text,
          isCompleted: editAdminTask?.isCompleted ?? false,
          completedAt: editAdminTask?.completedAt,
          dueDate:
              state.dueDate != null
                  ? state.dueTime != null
                      ? DateTime(
                        state.dueDate?.year ?? DateTime.now().year,
                        state.dueDate?.month ?? DateTime.now().month,
                        state.dueDate?.day ?? DateTime.now().day,
                        state.dueTime?.hour ?? DateTime.now().hour,
                        state.dueTime?.minute ?? DateTime.now().minute,
                      )
                      : state.dueDate ?? DateTime.now()
                  : DateTime.now(),
        );

        if (editAdminTask == null) {
          await adminTaskRepo.createAdminTask(adminTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Admin Task created successfully",
            ),
          );
          Navigator.of(context).pop();
        } else {
          await adminTaskRepo.updateAdminTask(adminTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Admin Task successfully",
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }
}
