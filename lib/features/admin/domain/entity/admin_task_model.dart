import 'package:cloud_firestore/cloud_firestore.dart';

class AdminTaskModel {
  final String docId;
  final String? projectId;
  final DateTime createdAt;
  final String createdBy;
  final String title;
  final String desc;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime dueDate;

  AdminTaskModel({
    required this.docId,
    this.projectId,
    required this.createdAt,
    required this.createdBy,
    required this.title,
    required this.desc,
    required this.isCompleted,
    this.completedAt,
    required this.dueDate,
  });

  AdminTaskModel copyWith({
    String? docId,
    String? projectId,
    DateTime? createdAt,
    String? createdBy,
    String? title,
    String? desc,
    bool? isCompleted,
    DateTime? completedAt,
    DateTime? dueDate,
  }) {
    return AdminTaskModel(
      docId: docId ?? this.docId,
      projectId: projectId ?? this.projectId,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      title: title ?? this.title,
      desc: desc ?? this.desc,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      dueDate: dueDate ?? this.dueDate,
    );
  }

  factory AdminTaskModel.fromJson(Map<String, dynamic> json) {
    return AdminTaskModel(
      docId: json['docId'] ?? '',
      projectId: json['projectId'],
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      completedAt:
          json['completedAt'] != null
              ? (json['completedAt'] as Timestamp).toDate()
              : null,
      dueDate: (json['dueDate'] as Timestamp).toDate(),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'title': title,
      'desc': desc,
      'isCompleted': isCompleted,
      'completedAt': completedAt,
      'dueDate': dueDate,
    };
  }

  factory AdminTaskModel.fromSnapshot(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return AdminTaskModel.fromJson({
      ...data,
      'docId': snap.id,
      // override docId from document ID
    });
  }

  Map<String, dynamic> toMap() {
    return {
      'createdAt': createdAt,
      'createdBy': createdBy,
      'title': title,
      'desc': desc,
      'isCompleted': isCompleted,
      'completedAt': completedAt,
      'dueDate': dueDate,
    };
  }

  factory AdminTaskModel.fromMap(Map<String, dynamic> map) {
    return AdminTaskModel(
      docId: map['docId'] ?? '',
      projectId: map['projectId'],
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      createdBy: map['createdBy'] ?? '',
      title: map['title'] ?? '',
      desc: map['desc'] ?? '',
      isCompleted: map['isCompleted'] ?? false,
      completedAt:
          map['completedAt'] != null
              ? (map['completedAt'] as Timestamp).toDate()
              : null,
      dueDate: (map['dueDate'] as Timestamp).toDate(),
    );
  }
}
