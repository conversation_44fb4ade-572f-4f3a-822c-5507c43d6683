import 'dart:async';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/records_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/request_model.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'attendance_state.dart';

class AttendanceCubit extends Cubit<AttendanceState> {
  FirebaseAttendanceRepo attendanceRepo;
  AttendanceCubit(this.attendanceRepo) : super(AttendanceState.initial());

  StreamSubscription<List<PunchesModel>>? punchesStream;
  StreamSubscription<List<RecordsModel>>? recordsStream;
  StreamSubscription<bool>? requestListener;
  StreamSubscription? lastPunchListener;

  RecordsModel? currentRecord;

  // PUNCH IN/OUT METHODS
  void punchIn(BuildContext context) {
    if (state.isLoading) {
      return;
    }
    emit(state.copyWith(isLoading: true, message: ''));

    try {
      // Punch in
      attendanceRepo.punchIn(FBAuth.auth.currentUser?.uid ?? '');

      Future.delayed(const Duration(seconds: 1), () {
        final userId = FBAuth.auth.currentUser?.uid ?? '';

        // Re-check punch status
        listenToLastPunch(userId);

        // Re-check request status
        showReqPunchOut(userId);
      });

      // send notification
      final adminUser =
          context
              .read<UserCubit>()
              .state
              .users
              .where((u) => u.role == 'admin')
              .first;

      final notification = NotificationModel(
        docId: '', // Firestore will auto-generate
        notifyType: NotificationType.attendence,
        title: "Punch In",
        message: "User : ${FBAuth.auth.currentUser?.email} punched in",
        token: adminUser.token ?? '',
        createdAt: DateTime.now(),
        users: [adminUser.docId],
      );
      context.read<NotificationFormCubit>().attendanceLogNotification(
        notification,
      );

      emit(state.copyWith(isLoading: false, message: 'Punch in successful'));
    } catch (e) {
      emit(state.copyWith(isLoading: false, message: 'Failed to punch in'));
    }
  }

  void punchOut(DateTime reqTime, BuildContext context) {
    if (state.isLoading) {
      return;
    }
    emit(state.copyWith(isLoading: true, message: ''));
    try {
      // Punch out
      attendanceRepo.punchOut(FBAuth.auth.currentUser?.uid ?? '', reqTime);

      final adminUser =
          context
              .read<UserCubit>()
              .state
              .users
              .where((u) => u.role == 'admin')
              .first;
      final notification = NotificationModel(
        docId: '', // Firestore will auto-generate
        notifyType: NotificationType.attendence,
        title: "Punch Out",
        message: "User : ${FBAuth.auth.currentUser?.email} punched out",
        token: adminUser.token ?? '',
        createdAt: DateTime.now(),
        users: [adminUser.docId],
      );
      context.read<NotificationFormCubit>().attendanceLogNotification(
        notification,
      );
      emit(state.copyWith(isLoading: false, message: 'Punch out successful'));
    } catch (e) {
      emit(state.copyWith(isLoading: false, message: 'Failed to punch out'));
    }
  }

  void requestPuchOutAfter7pm(DateTime reqTime, BuildContext context) async {
    if (state.isLoading) {
      return;
    }
    emit(state.copyWith(isLoading: true, message: ""));
    final userId = FBAuth.auth.currentUser?.uid ?? '';
    try {
      // if (DateTime.now().isAfter(req)) {
      //   await attendanceRepo.requestPunchOutAfter7pm(userId, req);
      // }
      if (DateTime.now().isAfter(reqTime)) {
        final now = DateTime.now();
        final requestRef = FBFireStore.requests;
        final requestDoc = requestRef.doc();
        final request = RequestModel(
          docId: requestDoc.id,
          uId: userId,
          createdAt: now,
          reqTime: reqTime,
          active: true,
        );
        await requestDoc.set(request.toJson());
        // Immediately re-check punch status
        listenToLastPunch(userId);
        // Immediately re-check request status
        showReqPunchOut(userId);

        // send notification
        final adminUser =
            context
                .read<UserCubit>()
                .state
                .users
                .where((u) => u.role == 'admin')
                .first;
        final notification = NotificationModel(
          docId: '', // Firestore will auto-generate
          notifyType: NotificationType.request,
          title: "Request Punch Out",
          message: "${FBAuth.auth.currentUser?.email} send punch out requeste",
          data: {'reqId': request.docId, 'hasApproved': false},
          token: adminUser.token ?? '',
          createdAt: DateTime.now(),
          users: [adminUser.docId],
        );
        context.read<NotificationFormCubit>().requestNotification(notification);
      }

      emit(state.copyWith(isLoading: false, message: 'Punch out requested'));
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: 'Failed to request punch out',
        ),
      );
    }
  }

  // FETCH METHODS
  void fetchUserPunches(String userId, DateTime day) {
    if (isClosed) return;
    emit(state.copyWith(isLoading: true, message: ""));
    punchesStream?.cancel();

    punchesStream = attendanceRepo
        .fetchUserDayPunches(userId, day)
        .listen(
          (punches) {
            print("PunchesStream user-${punches.length}");
            emit(
              state.copyWith(punches: punches, isLoading: false, message: ""),
            );
            applyFilter(userId);
            listenToLastPunch(userId);
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch punches: ${error.toString()}",
              ),
            );
          },
        );
  }

  Future<void> fetchUserMonthlyRecord(String userId, DateTime month) async {
    // print(month);
    emit(state.copyWith(isLoading: true, message: ""));

    try {
      final startOfMonth = DateTime(month.year, month.month);
      final startOfNextMonth = DateTime(month.year, month.month + 1);

      final querySnapshot =
          await FBFireStore.records
              .where('userId', isEqualTo: userId)
              .where('createdAt', isGreaterThanOrEqualTo: startOfMonth)
              .where('createdAt', isLessThan: startOfNextMonth)
              .limit(1)
              .get();

      RecordsModel? currentRecord;

      if (querySnapshot.docs.isNotEmpty) {
        currentRecord = RecordsModel.fromSnapshot(querySnapshot.docs.first);
      } else {
        currentRecord = RecordsModel(
          docId: '',
          userId: userId,
          createdAt: month,
          lastAttend: month,
          totalAttendance: 0,
          totalMinutes: 0,
        );
      }

      if (!isClosed) {
        emit(
          state.copyWith(
            isLoading: false,
            currentRecord: currentRecord,
            message: "",
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, message: "Error fetching record: $e"),
      );
    }
  }

  // LISTENERS METHODS
  void listenToRequestStatus(String userId) {
    requestListener?.cancel();

    requestListener = attendanceRepo.listenToActiveRequestStatus(userId).listen(
      (hasActiveRequest) {
        emit(state.copyWith(hasActiveRequest: hasActiveRequest));
      },
    );
  }

  void listenToLastPunch(String userId) {
    lastPunchListener?.cancel();
    lastPunchListener = attendanceRepo.listenToLastPunch(userId).listen((
      lastPunch,
    ) {
      emit(
        state.copyWith(
          lastPunch: lastPunch,
          isLoading: false,
          message: "",
          isPunchedIn: lastPunch?.punchIn ?? false,
        ),
      );
      showReqPunchOut(userId);
    });
  }

  void showReqPunchOut(String userId) async {
    emit(
      state.copyWith(showRequestPunchOut: false, message: "", isLoading: true),
    );

    final bool after7pm = DateTime.now().isAfter(reqPunchOutTime);
    DateTime userLastPunchedIn = DateTime(
      state.lastPunch?.createdAt.year ?? DateTime.now().year,
      state.lastPunch?.createdAt.month ?? DateTime.now().month,
      state.lastPunch?.createdAt.day ?? DateTime.now().day,
    );
    final bool isTodayPunchedIn =
        userLastPunchedIn ==
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
    emit(
      state.copyWith(
        showRequestPunchOut: after7pm || !isTodayPunchedIn,
        isLoading: false,
        message: "",
      ),
    );
    // emit(state.copyWith(lastPunch: lastPunch));
  }

  //UI METHODS
  void selectedFilter(String userId, String filter, {DateTime? customDate}) {
    final updatedDate = customDate ?? DateTime.now();

    emit(state.copyWith(selectedFilter: filter, selectedDate: updatedDate));
    if (filter != AttendanceFilter.month) {
      applyFilter(userId ?? FBAuth.auth.currentUser?.uid ?? '');
    }
    if (filter == AttendanceFilter.month) {
      fetchUserMonthlyRecord(FBAuth.auth.currentUser?.uid ?? '', updatedDate );
      // loadMonthlyRecord(updatedDate);
    }
  }

  void applyFilter(String? userId) async {
    final filter = state.selectedFilter;
    final punches = state.punches;
    final date = state.selectedDate;
    List<PunchesModel> filtered = [];

    if (filter == AttendanceFilter.custom) {
      final res =
          await FBFireStore.punches
              .where(
                'userId',
                isEqualTo: userId ?? FBAuth.auth.currentUser?.uid,
              )
              .where('createdAt', isGreaterThanOrEqualTo: date)
              .where('createdAt', isLessThan: date.add(Duration(days: 1)))
              .get();
      print("custom - ${res.docs.length}");
      filtered = res.docs.map((doc) => PunchesModel.fromSnapshot(doc)).toList();
    } else {
      filtered =
          punches.where((punch) {
            return punch.createdAt.year == date.year &&
                punch.createdAt.month == date.month &&
                punch.createdAt.day == date.day;
          }).toList();
      print("default - ${filtered.length}");
    }

    emit(state.copyWith(filteredPunches: filtered));
  }

  void selectMonth(int value) {
    emit(state.copyWith(selectedMonth: value));
  }

  Future<DateTime?> selectReqPunchOutDateTime(
    BuildContext context,
    DateTime initialDateTime,
  ) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDateTime,
      firstDate: DateTime(2022),
      lastDate: DateTime(2100),
    );

    if (pickedDate == null) return null;

    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDateTime),
    );

    if (pickedTime == null) return null;

    return DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
    );
  }

  @override
  Future<void> close() {
    punchesStream?.cancel();
    requestListener?.cancel();
    lastPunchListener?.cancel();
    return super.close();
  }
}
