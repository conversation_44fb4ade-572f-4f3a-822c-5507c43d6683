import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:cp_associates/features/task/domain/entity/task_logs_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/domain/repo/task_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
part 'taskform_state.dart';

class TaskFormCubit extends Cubit<TaskFormState> {
  TaskRepo taskRepo;
  TaskFormCubit(this.taskRepo) : super(TaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final taskNoteController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(TaskModel? editTask) {
    if (editTask != null) {
      titleController.text = editTask.title;
      descController.text = editTask.desc;

      emit(
        state.copyWith(
          priorityStatus: editTask.priority,
          taskStatus: editTask.status,
          startDate: editTask.startDate,
          endDate: editTask.endDate,
          selectedUser: editTask.assignTo,
          dbFile: editTask.attachments,
        ),
      );
    } else {
      emit(TaskFormState.initial());
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  // UI RELATED FUNCTION
  void priorityStatus(String priorityStatus) {
    if (priorityStatus.toLowerCase() == TaskSPrioritytatus.high.toLowerCase()) {
      emit(state.copyWith(priorityStatus: priorityStatus));
    } else if (priorityStatus.toLowerCase() ==
        TaskSPrioritytatus.mid.toLowerCase()) {
      emit(state.copyWith(priorityStatus: priorityStatus));
    } else {
      emit(state.copyWith(priorityStatus: priorityStatus));
    }
  }

  void selectStatus(String status) {
    if (status.toLowerCase() == TaskStatus.submitted.toLowerCase()) {
      emit(state.copyWith(taskStatus: status));
    } else if (status.toLowerCase() == TaskStatus.ongoing.toLowerCase()) {
      emit(state.copyWith(taskStatus: status));
    } else {
      emit(state.copyWith(taskStatus: status));
    }
  }

  void selectStartDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2026),
    );

    emit(state.copyWith(startDate: res));
  }

  void selectEndtDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2026),
    );

    emit(state.copyWith(endDate: res));
  }

  void updateSelectedUser(String userId) {
    emit(state.copyWith(selectedUser: userId));
  }

  void selectMasterTask(
    MasterTaskModel masterTask,
    BuildContext context,
    String projectId,
  ) {
    emit(state.copyWith(masterTaskId: masterTask.docId));
    if (masterTask.docId.isNotEmpty) {
      titleController.text = masterTask.title;
      descController.text = masterTask.desc;

      final projectUsers =
          context.read<ProjectCubit>().fetchProjectById(projectId) != null
              ? context.read<ProjectCubit>().fetchProjectById(projectId)!.userId
              : [];

      final allowedUserIds =
          context
              .read<UserCubit>()
              .state
              .users
              .where(
                (user) =>
                    projectUsers.contains(user.docId) || user.role == 'admin',
              )
              .map((u) => u.docId)
              .toList();

      final selectedUser =
          allowedUserIds.contains(masterTask.assignTo)
              ? masterTask.assignTo
              : '';
      emit(
        state.copyWith(
          selectedUser: selectedUser ?? '',
          startDate: DateTime.now(),
          endDate: DateTime.now().add(Duration(days: masterTask.duration)),
        ),
      );
    }
  }

  void createStatusLog(
    String status,
    TaskModel? task,
    String userId,
    BuildContext context,
  ) async {
    if (task == null) {
      return;
    }

    // Capture context-dependent data before async operations
    final projectCubit = context.read<ProjectCubit>();
    final userCubit = context.read<UserCubit>();
    final notificationFormCubit = context.read<NotificationFormCubit>();

    final project = projectCubit.fetchProjectById(task.projectId);

    final tasklog = TaskLogsModel(
      taskId: task.docId,
      createdAt: DateTime.now(),
      createdBy: userId,
      status: status,
    );

    try {
      // update task status
      await FBFireStore.tasks.doc(tasklog.taskId).update({"status": status});

      // create task log
      await FBFireStore.tasks
          .doc(tasklog.taskId)
          .collection("tasklogs")
          .add(tasklog.toJson());

      // create notification
      if (status.toLowerCase() == TaskStatus.submitted.toLowerCase()) {
        print("Task Submitted Notification");
        final user = userCubit.getUserById(task.createdBy);

        final token = user?.token;
        if (token != null && project != null) {
          print("Token: $token");
          final notification = NotificationModel(
            docId: '', // Firestore will auto-generate
            notifyType: NotificationType.sumbitTask,
            title: "${project.projectTitle} : Task Submitted",
            message: "Task : ${task.title} is submitted",
            data: {
              'projectId': task.projectId,
              'taskId': task.docId,
              'hasApproved': false,
            },
            token: token,
            createdAt: DateTime.now(),
            users: [task.createdBy],
          );
          notificationFormCubit.createNotification(notification);
        }
      }
      if (status.toLowerCase() == TaskStatus.approved.toLowerCase()) {
        print("Task Approved Notification");
        final user = userCubit.getUserById(task.assignTo);
        final token = user?.token;
        if (token != null && project != null) {
          print("Token: $token");
          final notification = NotificationModel(
            docId: '', // Firestore will auto-generate
            notifyType: NotificationType.approveTask,
            title: "${project.projectTitle} : Task Approved",
            message: "Task : ${task.title} is approved",
            data: {
              'projectId': task.projectId,
              'taskId': task.docId,
              'hasApproved': true,
            },
            token: token,
            createdAt: DateTime.now(),
            users: [task.assignTo],
          );
          notificationFormCubit.createNotification(notification);
        }
      }

      if (!isClosed) {
        emit(
          state.copyWith(
            taskStatus: status,
            message: "Status updated to $status",
          ),
        );
      }
    } catch (e) {
      print("Error in createStatusLog: $e");
      if (!isClosed) {
        emit(
          state.copyWith(message: "Failed to update status: ${e.toString()}"),
        );
      }
    }
  }

  // unapprove task
  void unapproveTask(
    TaskModel task,
    BuildContext context,
    String status,
  ) async {
    // Capture context-dependent data before async operations to avoid widget tree disposal issues
    final projectCubit = context.read<ProjectCubit>();
    final userCubit = context.read<UserCubit>();
    final notificationFormCubit = context.read<NotificationFormCubit>();

    final project = projectCubit.fetchProjectById(task.projectId);
    final assignedUser = userCubit.getUserById(task.assignTo);
    final assignedUserToken = assignedUser?.token;

    final tasklog = TaskLogsModel(
      taskId: task.docId,
      createdAt: DateTime.now(),
      createdBy: FBAuth.auth.currentUser?.uid ?? "",
      status: status,
      comment: taskNoteController.text,
    );

    try {
      // update task status
      await FBFireStore.tasks.doc(tasklog.taskId).update({"status": status});

      // create task log
      await FBFireStore.tasks
          .doc(tasklog.taskId)
          .collection("tasklogs")
          .add(tasklog.toJson());

      // Send notification to the assigned user about task being unapproved
      if (assignedUserToken != null && project != null) {
        print(
          "Sending unapprove notification to assigned user: ${assignedUser?.name}",
        );
        final notification = NotificationModel(
          docId: '', // Firestore will auto-generate
          notifyType: NotificationType.approveTask,
          title: "${project.projectTitle} : Task Unapproved",
          message:
              "Task '${task.title}' has been unapproved${taskNoteController.text.isNotEmpty ? ': ${taskNoteController.text}' : ''}",
          data: {'projectId': task.projectId, 'taskId': task.docId},
          token: assignedUserToken,
          createdAt: DateTime.now(),
          users: [task.assignTo], // Send to assigned user
        );
        notificationFormCubit.createNotification(notification);
      }

      if (!isClosed) {
        taskNoteController.clear();
        emit(
          state.copyWith(
            taskStatus: status,
            message: "Task unapproved successfully",
          ),
        );
      }
    } catch (e) {
      print("Error in unapproveTask: $e");
      if (!isClosed) {
        emit(
          state.copyWith(message: "Failed to unapprove task: ${e.toString()}"),
        );
      }
    }
  }

  void submit(
    TaskModel? editTask,
    String projectId,
    BuildContext context,
  ) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        // Capture context-dependent data before async operations
        final projectCubit = context.read<ProjectCubit>();
        final userCubit = context.read<UserCubit>();
        final notificationFormCubit = context.read<NotificationFormCubit>();

        //check file is from upload or database
        final fileUrl =
            state.selectedFile != null
                ? await FirebaseStorageRepo().uploadTaskFile(
                  state.selectedFile!,
                )
                : state.dbFile;

        //assing data
        final task = TaskModel(
          docId: editTask?.docId ?? "",
          createdAt: editTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          priority: state.priorityStatus,
          desc: descController.text,
          status: state.taskStatus ?? "",
          projectId: projectId,
          startDate: editTask?.startDate ?? state.startDate,
          endDate: editTask?.endDate ?? state.endDate ?? DateTime.now(),
          assignTo: state.selectedUser,
          title: titleController.text,
          attachments: fileUrl,
          attachmentType:
              state.selectedFile?.extension ?? editTask?.attachmentType ?? "",
          attachmentName:
              state.selectedFile?.name ?? editTask?.attachmentName ?? "",
        );

        if (editTask == null) {
          print("create task");
          // create task
          final taskId = await taskRepo.createTask(task);

          // update the task model with the real Firestore docId
          final taskWithId = task.copyWith(docId: taskId);

          // create task log
          createStatusLog(
            task.status,
            taskWithId,
            FBAuth.auth.currentUser?.uid ?? "",
            context,
          );

          // create notification

          // get token of assigned user
          final user = userCubit.getUserById(task.assignTo);
          print("create task user: ${user?.name}--${user?.token}");
          final token = user?.token;
          print("create task Token: $token");

          final project = projectCubit.fetchProjectById(projectId);

          if (token != null && project != null) {
            // print("create task notification");
            final notification = NotificationModel(
              docId: '',
              notifyType: NotificationType.task,
              title: "${project.projectTitle} : New Task",
              message: "Task : ${task.title} is assigned to you",
              data: {'projectId': task.projectId, 'taskId': taskId},
              token: token,
              createdAt: DateTime.now(),
              users: [task.assignTo],
            );

            // Call notification cubit to create notification document & send notification
            // print("Notification created");
            notificationFormCubit.createNotification(notification);
          }
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Task created successfully",
            ),
          );
          // project last update
          projectCubit.projectLastUpdateAt(projectId);
          Navigator.of(context).pop();
        } else {
          final oldStatus = editTask.status;
          final newStatus = task.status;

          // update task
          await taskRepo.updateTask(task);

          // create task log
          if (oldStatus.toLowerCase() != newStatus.toLowerCase()) {
            createStatusLog(
              newStatus,
              task,
              FBAuth.auth.currentUser?.uid ?? "",
              context,
            );
          }
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Task successfully",
            ),
          );
          // project last update
          projectCubit.projectLastUpdateAt(projectId);
          projectCubit.createLastSeen(projectId);
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }
}
