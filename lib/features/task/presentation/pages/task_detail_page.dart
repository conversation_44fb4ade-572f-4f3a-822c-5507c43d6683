import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_header_detail.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timelines_plus/timelines_plus.dart';

class TaskDetailPage extends StatefulWidget {
  String? taskId;
  TaskDetailPage({super.key, required this.taskId});

  @override
  State<TaskDetailPage> createState() => _TaskDetailPageState();
}

class _TaskDetailPageState extends State<TaskDetailPage> {
  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();

    return ResponsiveCustomBuilder(
      mobileBuilder: (width) {
        return Scaffold(
          appBar: AppBar(),
          body: BlocConsumer<TaskCubit, TaskState>(
            listener: (context, state) {
              if (state.message.isNotEmpty) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text(state.message)));
              }
            },
            builder: (context, state) {
              print("TaskDetailPage build");
              if (state.taskDetail != null) {
                final task = state.taskDetail;
                final createdUser = userCubit.getUserById(
                  task?.createdBy ?? "",
                );
                return SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    child: Column(
                      children: [
                        TaskDetailConatiner(task, createdUser),
                        SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: AppColors.containerGreyColor,
                            border: Border.all(color: AppColors.borderGrey),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(state.taskLogs.length, (
                              index,
                            ) {
                              final log = state.taskLogs[index];
                              final user = userCubit.getUserById(log.createdBy);

                              // Check if this is the last log
                              final isLast = index == state.taskLogs.length - 1;
                              //Check if this is the first log

                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Column(
                                    children: [
                                      Container(
                                        child:
                                            state.taskLogs.length == 1
                                                ? Text("")
                                                : DotIndicator(
                                                  color: Colors.grey,
                                                ),
                                      ),
                                      if (!isLast) ...[
                                        SizedBox(
                                          height: 90,
                                          child: SolidLineConnector(
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                  SizedBox(width: 10),
                                  // Content side
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            CircleAvatar(
                                              radius: 15,
                                              child: Text(
                                                user?.name[0].toUpperCase() ??
                                                    "",
                                              ),
                                            ),
                                            SizedBox(width: 15),
                                            Text(
                                              user?.name ?? "",
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 10),
                                        Row(
                                          children: [
                                            Text(
                                              log.status,
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            Spacer(),
                                            Text(log.createdAt.goodDayDate()),
                                          ],
                                        ),
                                        SizedBox(height: 10),
                                        Text(
                                          truncateText(task?.desc ?? "", 40),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (state.isLoading) {
                return Center(child: CircularProgressIndicator());
              } else {
                return Center(child: Text("No Task Detail Found"));
              }
            },
          ),
        );
      },
    );
  }

  Widget TaskDetailConatiner(TaskModel? task, UserModel? createdUser) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.containerGreyColor,
        border: Border.all(color: AppColors.borderGrey),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          TaskHeaderDetail(task: task),
          SizedBox(height: 20),
          Text(task?.desc ?? ""),
          SizedBox(height: 20),
          task?.attachments != null
              ? Text("Attachments", style: AppTextStyles.label)
              : SizedBox(),
          SizedBox(height: 10),
          task?.attachments != null
              ? buildFilePreview(
                context: context,
                selectedFile: null,
                dbFile: task?.attachments,
                dbFileExt: task?.attachmentType,
                dbFileName: task?.attachmentName,
                isEdit: false,
                onDelete: () {},
                onView: () {
                  viewFile(
                    context: context,
                    selectedFile: null,
                    dbImg: task?.attachments,
                    dbImgExt: task?.attachmentType,
                  );
                },
                isMessage: false,
              )
              : SizedBox(),
          SizedBox(height: 20),
          Row(
            children: [
              Text("End Date", style: AppTextStyles.label),
              Spacer(),
              Text(task?.endDate.goodDayDate() ?? ""),
            ],
          ),
          SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: buildStatusButton(
                  task,
                  FBAuth.auth.currentUser?.uid ?? "",
                  context.read<AuthCubit>().state.currentUser?.role == "admin",
                ),
              ),
            ],
          ),
          SizedBox(height: 10),
          Center(child: Text("Task created by ${createdUser?.name}")),
        ],
      ),
    );
  }

  Widget buildStatusButton(TaskModel? task, String userId, bool isAdmin) {
    final taskFormCubit = context.read<TaskFormCubit>();
    if (task?.status.toLowerCase() == TaskStatus.pending.toLowerCase()) {
      return IgnorePointer(
        ignoring: !(task?.assignTo == userId || isAdmin),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor:
                !(task?.assignTo == userId || isAdmin)
                    ? Colors.grey.shade400
                    : AppColors.primary,
          ),
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.ongoing);
            taskFormCubit.createStatusLog(
              TaskStatus.ongoing,
              task,
              userId,
              context,
            );
          },
          child: Text("Start Task", style: AppTextStyles.button),
        ),
      );
    }

    if (task?.status == TaskStatus.ongoing) {
      return IgnorePointer(
        ignoring: !(task?.assignTo == userId || isAdmin),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor:
                !(task?.assignTo == userId || isAdmin)
                    ? Colors.grey.shade400
                    : AppColors.primary,
          ),
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.submitted);
            taskFormCubit.createStatusLog(
              TaskStatus.submitted,
              task,
              userId,
              context,
            );
          },
          child: Text("Submit Task"),
        ),
      );
    }

    if (task?.status == TaskStatus.submitted) {
      final isNotAssignee = task?.assignTo != userId;
      return IgnorePointer(
        ignoring: (!isAdmin || isNotAssignee),
        child: ElevatedButton(
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.approved);
            taskFormCubit.createStatusLog(
              TaskStatus.approved,
              task,
              userId,
              context,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor:
                (isAdmin && isNotAssignee)
                    ? AppColors.primary
                    : Colors.grey.shade400,
          ),
          child: Text("Approve Task"),
        ),
      );
    }

    if (task?.status == TaskStatus.approved) {
      return IgnorePointer(
        ignoring: true,
        child: ElevatedButton.icon(
          onPressed: () {},
          label: Text(
            "Completed",
            style: AppTextStyles.button.copyWith(color: Colors.green),
          ),
          icon: Icon(CupertinoIcons.check_mark_circled, color: Colors.green),
          style: ElevatedButton.styleFrom(
            elevation: 0,
            backgroundColor: AppColors.containerGreyColor,
            //border
            side: BorderSide(color: AppColors.borderGrey),
          ),
        ),
      );
    }

    return SizedBox();
  }
}
