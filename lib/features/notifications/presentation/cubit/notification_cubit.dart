import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/notifications/data/notification_firbaserepo.dart';
import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'notification_state.dart';

class NotificationCubit extends Cubit<NotificationState> {
  FirebaseNotificationRepo notificationRepo;
  NotificationCubit({required this.notificationRepo})
    : super(NotificationState.initial());

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  StreamSubscription<List<NotificationModel>>? notificationStream;

  // NOTIFIACION INIT FUNCTIONS
  void requestNotificationPermission() async {
    NotificationSettings settings = await FBMessaging.messaging
        .requestPermission(
          alert: true,
          announcement: true,
          badge: true,
          carPlay: true,
          criticalAlert: true,
          provisional: true,
          sound: true,
        );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
      emit(state.copyWith(isPermissionGranted: true));
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      emit(state.copyWith(isPermissionGranted: true));
      print('User granted provisional permission');
    } else {
      emit(state.copyWith(isPermissionGranted: false));
      print('User declined or has not accepted permission');
    }
  }

  Future<String> getDeviceToken() async {
    String? token = await FBMessaging.messaging.getToken();
    print("Device token: $token");
    return token ?? "";
  }

  void isTokenRefreshed() {
    FBMessaging.messaging.onTokenRefresh.listen((event) {
      print("Token refreshed: $event");
    });
  }

  // LOCAL NOTIFICATION FUNCTIONS
  void initLocalNotification(
    BuildContext context,
    RemoteMessage message,
  ) async {
    var androidInitializationSettingsAndroid = AndroidInitializationSettings(
      'ic_stat_notification',
    );
    var iosInitializationSettings = DarwinInitializationSettings();

    var initializationSettings = InitializationSettings(
      android: androidInitializationSettingsAndroid,
      iOS: iosInitializationSettings,
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (payload) {
        if (payload.payload != null) {
          handleMessage(message);
        }
      },
    );
  }

  // LISTEN TO NOTIFICATION
  void listenToNotification(BuildContext context) {
    FirebaseMessaging.onMessage.listen((message) {
      print("Notification received: ${message.notification?.title}");
      print("Notification received: ${message.notification?.body.toString()}");
      print("Notification received meta: ${message.data}");

      if (Platform.isAndroid) {
        initLocalNotification(context, message);
        showNotification(message);
      } else {
        showNotification(message);
      }
    });
  }

  // show notification
  void showNotification(RemoteMessage message) async {
    try {
      // Create the notification channel
      const channel = AndroidNotificationChannel(
        'high_importance_channel', // id
        'High Importance Notifications', // title
        importance: Importance.max,
      );

      // Create the channel (this is safe to call multiple times)
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);

      var androidDetails = AndroidNotificationDetails(
        channel.id,
        channel.name,
        channelDescription: "high importance channel ",
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'ticker',
        icon: 'ic_stat_notification', // Add the icon parameter
      );

      var iosDetails = const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      var platformDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await flutterLocalNotificationsPlugin.show(
        0,
        message.notification?.title ?? 'Notification',
        message.notification?.body ?? '',
        platformDetails,
        payload: message.data['id'],
      );
    } catch (e) {
      print('Error showing notification: $e');
    }
  }

  //handel msg onTap on background
  Future<void> handelBackgroundMessage() async {
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    // when app is closed and msg is clicked
    if (initialMessage != null) {
      handleMessage(initialMessage);
    }

    // when app is in background and msg is clicked
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      handleMessage(message);
    });
  }

  // handel msg on tap redirect INapp
  void handleMessage(RemoteMessage message) {
    final type = message.data['type'];

    switch (type) {
      case 'activity':
        final projectId = message.data['projectId'];
        if (projectId != null && projectId.isNotEmpty) {
          kIsWeb
              ? appRoute.go('${Routes.activity}/$projectId')
              : appRoute.push('${Routes.activity}/$projectId');
        }
        break;

      case 'task':
        final taskId = message.data['taskId'];
        if (taskId != null && taskId.isNotEmpty) {
          kIsWeb
              ? appRoute.go('${Routes.taskDetail}/$taskId')
              : appRoute.push('${Routes.taskDetail}/$taskId');
        }
        break;

      case 'submitTask':
        final taskId = message.data['taskId'];
        if (taskId != null && taskId.isNotEmpty) {
          appRoute.go('${Routes.taskDetail}/$taskId');
        }
        break;

      case 'request':
        final requestId = message.data['requestId'];
        if (requestId != null && requestId.isNotEmpty) {
          appRoute.go('${Routes.notification}');
        }
        break;

      case 'attendence':
        appRoute.go('${Routes.userAttendance}');
        break;

      default:
        print("Unhandled notification type: $type");
    }
  }

  // TOPIC FUNCTIONS
  void subscribeToTopic(String topic) {
    print("Subscribed to topic: $topic");
    FBMessaging.messaging.subscribeToTopic(topic);

    FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
      'topics': FieldValue.arrayUnion([topic]),
    });
  }

  void unsubscribeFromTopic(String topic) {
    FBMessaging.messaging.unsubscribeFromTopic(topic);
  }

  // IOS NOTIFICATION FUNCTIONS
  // Future forgroundMessage() async {
  //   FirebaseMessaging.onMessage.listen((message) {
  //     print("Notification received: ${message.notification?.title}");
  //     print("Notification received: ${message.notification?.body.toString()}");
  //     print("Notification received meta: ${message.data}");
  //   });
  // }

  // unread notificationt functions
  Future<void> setNotificationlastSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'last_seen_notification',
      DateTime.now().toIso8601String(),
    );
  }

  void fetchUnreadNotification() async {
    emit(state.copyWith(isLoading: true, message: ''));
    // print("lastSeen: ${project.docId}--");
    final prefs = await SharedPreferences.getInstance();
    final seen = prefs.getString('last_seen_notification');

    final seenTime = DateTime.tryParse(seen ?? '');

    // print("lastSeen: $seenTime");

    notificationStream?.cancel();
    notificationStream = notificationRepo
        .fetchNotificationBeforeLastSeen(seenTime ?? DateTime.now())
        .listen((notifications) {
          print("NotificationStream-${notifications.length}");
          emit(
            state.copyWith(
              isLoading: false,
              message: '',
              notifications: notifications,
              hasUnreadNotification: notifications.isNotEmpty,
            ),
          );
        });
  }

  void markNotificationAsRead(String notificationId) {
    notificationRepo.updateNotification(notificationId);
  }

  void approvedNotificationTaskAndCreateLog(
    String taskId,
    String notificationId,
  ) async {
    emit(state.copyWith(isLoading: true, message: ''));
    print("approvedNotificationTaskAndCreateLog");
    print("taskId: $taskId");
    final task = await FBFireStore.tasks.doc(taskId).update({
      "status": TaskStatus.approved,
    });
    await FBFireStore.tasks.doc(taskId).collection("tasklogs").add({
      "taskId": taskId,
      "createdAt": DateTime.now(),
      "createdBy": FBAuth.auth.currentUser?.uid ?? "",
      "status": TaskStatus.approved,
    });

    await FBFireStore.notifications.doc(notificationId).update({
      "data.hasApproved": true,
    });
    emit(state.copyWith(isLoading: false, message: 'Task approved'));
  }
}
