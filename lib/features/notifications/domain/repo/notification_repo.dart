import 'package:cp_associates/features/notifications/domain/entity/notication_model.dart';

abstract class NotificationRepo {
  // Fetch Notification before last seen
  Stream<List<NotificationModel>> fetchNotificationBeforeLastSeen(
    DateTime lastSeen,
  );
  Future<void> updateNotification(String notificationId);

  // Create notification Functions
  Future<void> createActivityNotification(NotificationModel notification);
  Future<void> createTaskNotification(NotificationModel notification);
  Future<void> submitTaskNotification(NotificationModel notification);
  Future<void> attendanceLogNotification(NotificationModel notification);
  Future<void> requestNotification(NotificationModel notification);
}
