import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/admin/data/firebase_admintask_repo.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/home/<USER>/setting_firebase_repo.dart';
import 'package:cp_associates/features/home/<USER>/cubit/setting_cubit.dart';
import 'package:cp_associates/features/mastertask/data/firebase_mastertask_repo.dart';
import 'package:cp_associates/features/mastertask/presentation/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/notifications/data/notification_firbaserepo.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_cubit.dart';
import 'package:cp_associates/features/notifications/presentation/cubit/notification_form_cubit.dart';
import 'package:cp_associates/features/project/data/fb_project_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/users/data/firebase_user_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core/theme/app_theme.dart';
import 'core/widgets/responsive_widget.dart';
import 'features/auth/data/fb_auth_repo.dart';
import 'features/auth/presentation/cubits/auth_cubit.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';

Future<void> firebasemessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();

  print('Handling a background message: ${message.messageId}');
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final firebaseAuthRepo = FirebaseAuthRepo();
  final firebaseProjectRepo = FirebaseProjectRepo();
  final firebaseUserRepo = FirebaseUserRepo();
  final firebaseMasterTaskRepo = FirebaseMasterTaskRepo();
  final firebaseAdminTaskRepo = FirebaseAdminTaskRepo();
  final firebaseAttendanceRepo = FirebaseAttendanceRepo();
  final firebaseTaskRepo = FirebaseTaskRepo();
  final firebaseSettingRepo = SettingFirebaseRepo();
  final firebaseNotificationRepo = FirebaseNotificationRepo();

  // @override
  @override
  Widget build(BuildContext context) {
    // print("build");
    return BlocProvider<AuthCubit>(
      create: (_) => AuthCubit(authRepo: firebaseAuthRepo)..checkAuth(context),
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          final isAdmin = state.currentUser?.role == "admin";
          return isAdmin
              ? MultiBlocProvider(
                providers: [
                  BlocProvider<NotificationCubit>(
                    create: (context) {
                      return NotificationCubit(
                          notificationRepo: firebaseNotificationRepo,
                        )
                        ..requestNotificationPermission()
                        ..forgroundMessage()
                        ..getDeviceToken()
                        ..isTokenRefreshed()
                        ..listenToNotification(context)
                        ..handelBackgroundMessage()
                        ..fetchUnreadNotification();
                    },
                  ),
                  BlocProvider<NotificationFormCubit>(
                    create:
                        (_) => NotificationFormCubit(firebaseNotificationRepo),
                  ),
                  BlocProvider<MasterTaskCubit>(
                    create: (_) => MasterTaskCubit(firebaseMasterTaskRepo),
                  ),
                  BlocProvider<UserCubit>(
                    create:
                        (_) =>
                            UserCubit(firebaseUserRepo, firebaseAuthRepo)
                              ..fetchAllUsers(),
                  ),
                  BlocProvider<AttendanceAdminCubit>(
                    create:
                        (_) =>
                            AttendanceAdminCubit(firebaseAttendanceRepo)
                              ..fetchActiveRequests()
                              ..fetchAllUserToadyPunchesRecord(),
                  ),
                  BlocProvider<ProjectCubit>(
                    create:
                        (_) =>
                            ProjectCubit(firebaseProjectRepo)
                              ..fetchProjects()
                              ..totalProjectCount(),
                  ),
                  BlocProvider<AdminTaskCubit>(
                    create:
                        (_) =>
                            AdminTaskCubit(firebaseAdminTaskRepo)
                              ..fetchAllAdminTask(),
                  ),
                  BlocProvider<AttendanceCubit>(
                    create:
                        (_) => AttendanceCubit(firebaseAttendanceRepo)
                          ..listenToLastPunch(
                            FBAuth.auth.currentUser?.uid ?? '',
                          ),
                  ),
                  BlocProvider<TaskCubit>(
                    create: (_) => TaskCubit(firebaseTaskRepo),
                  ),
                  BlocProvider<TaskFormCubit>(
                    create: (_) => TaskFormCubit(firebaseTaskRepo),
                  ),
                  BlocProvider<SettingCubit>(
                    create:
                        (_) => SettingCubit(firebaseSettingRepo)..getSettings(),
                  ),
                ],
                child: ResponsiveWid(
                  mobile: ScreenUtilInit(
                    designSize: const Size(430, 932),
                    minTextAdapt: true,
                    builder:
                        (_, __) => MaterialApp.router(
                          debugShowCheckedModeBanner: false,
                          theme: AppTheme.lightTheme,
                          routerConfig: appRoute,
                        ),
                  ),
                  desktop: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    theme: AppTheme.lightTheme,
                    routerConfig: appRoute,
                  ),
                ),
              )
              : MultiBlocProvider(
                providers: [
                  BlocProvider<NotificationCubit>(
                    create:
                        (_) =>
                            NotificationCubit(
                                notificationRepo: firebaseNotificationRepo,
                              )
                              ..requestNotificationPermission()
                              ..forgroundMessage()
                              ..getDeviceToken()
                              ..isTokenRefreshed()
                              ..listenToNotification(context)
                              ..handelBackgroundMessage()
                              ..fetchUnreadNotification(),
                  ),
                  BlocProvider<NotificationFormCubit>(
                    create:
                        (_) => NotificationFormCubit(firebaseNotificationRepo),
                  ),
                  BlocProvider<MasterTaskCubit>(
                    create: (_) => MasterTaskCubit(firebaseMasterTaskRepo),
                  ),

                  BlocProvider<UserCubit>(
                    create:
                        (_) =>
                            UserCubit(firebaseUserRepo, firebaseAuthRepo)
                              ..fetchAllUsers(),
                  ),
                  BlocProvider<AttendanceCubit>(
                    create:
                        (_) => AttendanceCubit(firebaseAttendanceRepo)
                          ..listenToLastPunch(
                            FBAuth.auth.currentUser?.uid ?? '',
                          ),
                  ),

                  BlocProvider<AdminTaskCubit>(
                    create:
                        (_) => AdminTaskCubit(firebaseAdminTaskRepo)
                          ..fetchAdminTaskCreatedByCurrentUser(
                            FBAuth.auth.currentUser?.uid ?? '',
                          ),
                  ),

                  BlocProvider<ProjectCubit>(
                    create:
                        (context) =>
                            ProjectCubit(firebaseProjectRepo)
                              ..fetchProjectsOfCurrentUser(
                                context.read<NotificationCubit>(),
                              )
                              ..totalProjectCount(),
                  ),
                  BlocProvider<TaskCubit>(
                    create: (_) => TaskCubit(firebaseTaskRepo),
                  ),
                  BlocProvider<TaskFormCubit>(
                    create: (_) => TaskFormCubit(firebaseTaskRepo),
                  ),
                  BlocProvider<SettingCubit>(
                    create:
                        (_) => SettingCubit(firebaseSettingRepo)..getSettings(),
                  ),
                ],
                child: ResponsiveWid(
                  mobile: ScreenUtilInit(
                    designSize: const Size(430, 932),
                    minTextAdapt: true,
                    builder:
                        (_, __) => MaterialApp.router(
                          debugShowCheckedModeBanner: false,
                          theme: AppTheme.lightTheme,
                          routerConfig: appRoute,
                        ),
                  ),
                  desktop: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    theme: AppTheme.lightTheme,
                    routerConfig: appRoute,
                  ),
                ),
              );
        },
      ),
    );
  }
}
